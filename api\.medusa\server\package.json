{"name": "medusa-starter-default", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "postinstall": "npx postinstall"}, "dependencies": {"@lexical/file": "^0.19.0", "@lexical/react": "^0.19.0", "@medusajs/admin-sdk": "2.7.1", "@medusajs/cli": "2.7.1", "@medusajs/framework": "2.7.1", "@medusajs/medusa": "2.7.1", "@mikro-orm/core": "6.4.5", "@mikro-orm/knex": "6.4.5", "@mikro-orm/migrations": "6.4.5", "@mikro-orm/postgresql": "6.4.5", "@payos/node": "^1.0.10", "@rokmohar/medusa-plugin-meilisearch": "^1.1.4", "awilix": "^8.0.1", "axios": "^1.7.7", "katex": "^0.16.11", "lexical": "^0.19.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.454.0", "moment": "^2.30.1", "pg": "^8.13.0", "prettier": "^3.3.3", "react-dropzone": "^14.2.10"}, "devDependencies": {"@medusajs/test-utils": "latest", "@mikro-orm/cli": "6.4.5", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.12", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11"}, "engines": {"node": ">=20"}}